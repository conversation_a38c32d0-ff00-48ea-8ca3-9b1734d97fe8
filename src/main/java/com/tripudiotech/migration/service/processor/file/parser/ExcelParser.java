package com.tripudiotech.migration.service.processor.file.parser;

import com.tripudiotech.migration.dto.request.ParseSetting;
import com.tripudiotech.migration.exception.FileImportException;
import io.smallrye.mutiny.Multi;
import jakarta.enterprise.context.ApplicationScoped;
import lombok.extern.slf4j.Slf4j;
import org.dhatim.fastexcel.reader.Cell;
import org.dhatim.fastexcel.reader.CellType;
import org.dhatim.fastexcel.reader.ReadableWorkbook;
import org.dhatim.fastexcel.reader.Row;
import org.dhatim.fastexcel.reader.Sheet;

import java.io.InputStream;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@ApplicationScoped
public class ExcelParser implements FileParser, ParserConfigurable {

    private static final String XLSX = "XLSX";
    private static final String XLS = "XLS";

    private static final Set<String> SUPPORTED_TYPES = Set.of(XLSX, XLS);
    private String sheetName;
    private Integer sheetIndex = 0;

    @Override
    public Set<String> getSupportedTypes() {
        return SUPPORTED_TYPES;
    }

    @Override
    public Multi<String[]> parse(InputStream fileStream) {
        return Multi.createFrom().emitter(emitter -> {
            try (ReadableWorkbook workbook = new ReadableWorkbook(fileStream)) {
                Sheet sheet;

                // Use sheet by name if specified
                if (sheetName != null && !sheetName.isEmpty()) {
                    Optional<Sheet> optionalSheet = workbook.findSheet(sheetName);
                    if (optionalSheet.isEmpty()) {
                        log.warn("Sheet with name '{}' not found, falling back to sheet index {}",
                                sheetName, sheetIndex);
                        sheet = getSheetByIndex(workbook);
                    } else {
                        sheet = optionalSheet.get();
                    }
                } else {
                    // Use sheet by index
                    sheet = getSheetByIndex(workbook);
                }

                if (sheet == null) {
                    emitter.fail(new FileImportException("No sheet found in Excel file"));
                    return;
                }

                log.debug("Processing Excel sheet: {}", sheet.getName());

                // Filter out empty rows and skip header (first row)
                sheet.openStream()
                        .skip(0)  // Skip header row
                        .filter(row -> !isRowEmpty(row))
                        .forEach(row -> {
                            List<String> rowData = new ArrayList<>();
                            for (int i = 0; i < row.getCellCount(); i++) {
                                rowData.add(getCellValueAsString(row, i));
                            }
                            emitter.emit(rowData.toArray(new String[0]));
                        });

                emitter.complete();
            } catch (Exception e) {
                log.error("Error parsing Excel file", e);
                emitter.fail(new FileImportException("Failed to parse Excel file: " + e.getMessage(), e));
            }
        });
    }

    private Sheet getSheetByIndex(ReadableWorkbook workbook) throws Exception {
        try {
            // Collect sheets into a list to check size and get by index
            List<Sheet> sheets = workbook.getSheets().collect(Collectors.toList());

            if (sheets.isEmpty()) {
                return null;
            }

            if (sheetIndex >= sheets.size()) {
                log.warn("Sheet index {} is out of bounds (max: {}), using first sheet",
                        sheetIndex, sheets.size() - 1);
                return sheets.get(0);
            }

            return sheets.get(sheetIndex);
        } catch (Exception e) {
            log.error("Error accessing sheet by index {}", sheetIndex, e);
            throw e;
        }
    }

    private boolean isRowEmpty(Row row) {
        if (row == null || row.getCellCount() == 0) {
            return true;
        }

        // Check if all cells are empty or contain only whitespace
        boolean allCellsEmpty = true;
        for (int i = 0; i < row.getCellCount(); i++) {
            String cellText = null;
            try {
                cellText = row.getCellText(i);
            } catch (Exception e) {
                // If we can't get text, consider it empty
                continue;
            }

            if (cellText != null && !cellText.trim().isEmpty()) {
                allCellsEmpty = false;
                break;
            }
        }

        return allCellsEmpty;
    }


    private static final DateTimeFormatter[] DATE_FORMATTERS = {
            DateTimeFormatter.ofPattern("yyyy-MM-dd"),
            DateTimeFormatter.ofPattern("MM/dd/yyyy"),
            DateTimeFormatter.ofPattern("dd/MM/yyyy"),
            DateTimeFormatter.ofPattern("yyyy/MM/dd")
    };

    private static final DateTimeFormatter[] DATETIME_FORMATTERS = {
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"),
            DateTimeFormatter.ofPattern("MM/dd/yyyy HH:mm:ss"),
            DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm:ss"),
            DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss"),
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm")
    };

    private static final DateTimeFormatter[] TIME_FORMATTERS = {
            DateTimeFormatter.ofPattern("HH:mm:ss"),
            DateTimeFormatter.ofPattern("HH:mm"),
            DateTimeFormatter.ofPattern("h:mm a"),
            DateTimeFormatter.ofPattern("h:mm:ss a")
    };


    public String getCellValueAsString(Row row, int columnIndex) {
        try {
            Optional<Cell> cellOpt = row.getOptionalCell(columnIndex);

            if (cellOpt.isEmpty()) {
                return "";
            }

            Cell cell = cellOpt.get();


            return switch (cell.getType()) {
                case NUMBER -> {
                    if (isDateCell(cell)) {
                        yield formatDateFromExcelNumber(cell.asNumber().doubleValue());
                    }
                    yield String.valueOf(cell.asNumber());
                }
                case STRING -> cell.asString().trim();
                case BOOLEAN -> String.valueOf(cell.asBoolean());
                case ERROR -> "#ERROR";
                default -> "";
            };
        } catch (Exception e) {
            log.warn("Error reading cell at row {}, column {}: {}",
                    row.getRowNum(), columnIndex, e.getMessage());
            return "";
        }
    }

    private boolean isDateCell(Cell cell) {
        if (cell.getType() != CellType.NUMBER) {
            return false;
        }
        // Excel dates are numbers between 1 and 2958466
        double value = cell.asNumber().doubleValue();
        return value > 1 && value < 2958466;
    }

    private LocalDate excelNumberToLocalDate(double excelDate) {
        // Excel epoch is 1900-01-01 (with leap year bug)
        LocalDate excelEpoch = LocalDate.of(1900, 1, 1);
        return excelEpoch.plusDays((long) excelDate - 2); // -2 to account for Excel's leap year bug
    }

    private String formatDateFromExcelNumber(double excelDate) {
        LocalDate date = excelNumberToLocalDate(excelDate);
        return date.format(DateTimeFormatter.ISO_LOCAL_DATE);
    }

    private String parseAndFormatDateTime(String value) {
        // Try parsing as date first
        Optional<LocalDate> date = parseStringAsDate(value);
        if (date.isPresent()) {
            return date.get().format(DateTimeFormatter.ISO_LOCAL_DATE);
        }

        // Try parsing as datetime
        Optional<LocalDateTime> dateTime = parseStringAsDateTime(value);
        if (dateTime.isPresent()) {
            return dateTime.get().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
        }
        // Try parsing as time
        Optional<LocalTime> time = parseStringAsTime(value);
        return time.map(localTime -> localTime.format(DateTimeFormatter.ISO_LOCAL_TIME)).orElse(value);
    }

    private Optional<LocalDate> parseStringAsDate(String dateStr) {
        for (DateTimeFormatter formatter : DATE_FORMATTERS) {
            try {
                return Optional.of(LocalDate.parse(dateStr, formatter));
            } catch (Exception e) {
                // Continue to next formatter
            }
        }
        return Optional.empty();
    }

    private Optional<LocalDateTime> parseStringAsDateTime(String dateTimeStr) {
        for (DateTimeFormatter formatter : DATETIME_FORMATTERS) {
            try {
                return Optional.of(LocalDateTime.parse(dateTimeStr, formatter));
            } catch (Exception e) {
                // Continue to next formatter
            }
        }
        return Optional.empty();
    }

    private Optional<LocalTime> parseStringAsTime(String timeStr) {
        for (DateTimeFormatter formatter : TIME_FORMATTERS) {
            try {
                return Optional.of(LocalTime.parse(timeStr, formatter));
            } catch (Exception e) {
                // Continue to next formatter
            }
        }
        return Optional.empty();
    }

    @Override
    public void configure(ParseSetting settings) {
        if (settings == null) {
            return;
        }

        if (settings.getSheetName() != null) {
            this.sheetName = settings.getSheetName();
        }

        if (settings.getSheetIndex() != null) {
            this.sheetIndex = settings.getSheetIndex();
        }

        log.debug("Configured Excel parser with sheetName='{}', sheetIndex={}",
                this.sheetName, this.sheetIndex);
    }
}