package com.tripudiotech.migration.util;

import com.tripudiotech.migration.dto.request.ParseSetting;
import com.tripudiotech.migration.service.processor.file.parser.ExcelParser;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.nio.file.Path;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertArrayEquals;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.fail;

public class ExcelParserTest {

    private ExcelParser excelParser;
    
    @TempDir
    Path tempDir;
    
    @BeforeEach
    public void setUp() {
        excelParser = new ExcelParser();
    }
    
    @Test
    public void testGetSupportedTypes() {
        assertEquals(2, excelParser.getSupportedTypes().size());
        assertTrue(excelParser.getSupportedTypes().contains("XLSX"));
        assertTrue(excelParser.getSupportedTypes().contains("XLS"));
    }
    
    @Test
    public void testConfigure() {
        ParseSetting settings = new ParseSetting();
        settings.setSheetName("TestSheet");
        settings.setSheetIndex(2);
        
        excelParser.configure(settings);
        
        // Verify settings were applied using reflection
        try {
            java.lang.reflect.Field sheetNameField = ExcelParser.class.getDeclaredField("sheetName");
            sheetNameField.setAccessible(true);
            assertEquals("TestSheet", sheetNameField.get(excelParser));
            
            java.lang.reflect.Field sheetIndexField = ExcelParser.class.getDeclaredField("sheetIndex");
            sheetIndexField.setAccessible(true);
            assertEquals(2, sheetIndexField.get(excelParser));
        } catch (Exception e) {
            fail("Failed to access private fields: " + e.getMessage());
        }
    }

    @Test
    public void testParseWithRealWorkbook() throws Exception {
        File excelFile = createTestExcelFile();

        ParseSetting settings = new ParseSetting();
        settings.setSheetIndex(0);
        excelParser.configure(settings);

        try (FileInputStream fis = new FileInputStream(excelFile)) {
            List<String[]> results = excelParser.parse(fis)
                    .collect().asList()
                    .await().indefinitely();

            assertEquals(3, results.size());
            assertArrayEquals(new String[]{"Header1", "Header2", "Header3"}, results.get(0));
            assertArrayEquals(new String[]{"A1", "B1", "C1"}, results.get(1));
            assertArrayEquals(new String[]{"A2", "B2", "C2"}, results.get(2));
        }
    }
    
    @Test
    public void testParseWithSheetByName() throws Exception {
        // Create test workbook with multiple sheets
        File excelFile = createMultiSheetExcelFile();
        
        // Configure parser to use sheet by name
        ParseSetting settings = new ParseSetting();
        settings.setSheetName("SecondSheet");
        excelParser.configure(settings);
        
        // Parse the workbook
        try (FileInputStream fis = new FileInputStream(excelFile)) {
            List<String[]> results = excelParser.parse(fis)
                    .collect().asList()
                    .await().indefinitely();
            
            // Verify we got 1 row from second sheet
            assertEquals(2, results.size());
            
            // Verify row content 
            assertArrayEquals(new String[]{"X1", "Y1"}, results.get(1));
        }
    }
    
    @Test
    public void testParseWithFallbackToIndex() throws Exception {
        File excelFile = createMultiSheetExcelFile();

        ParseSetting settings = new ParseSetting();
        settings.setSheetName("NonExistentSheet");
        settings.setSheetIndex(0);
        excelParser.configure(settings);

        try (FileInputStream fis = new FileInputStream(excelFile)) {
            List<String[]> results = excelParser.parse(fis)
                    .collect().asList()
                    .await().indefinitely();

            assertEquals(3, results.size());
            assertArrayEquals(new String[]{"Header1", "Header2", "Header3"}, results.get(0));
            assertArrayEquals(new String[]{"A1", "B1", "C1"}, results.get(1));
            assertArrayEquals(new String[]{"A2", "B2", "C2"}, results.get(2));
        }
    }
    
    @Test
    public void testParseEmptyExcelFile() throws Exception {
        // Create empty Excel file
        File emptyFile = tempDir.resolve("empty.xlsx").toFile();
        
        try (Workbook workbook = new XSSFWorkbook();
             FileOutputStream outputStream = new FileOutputStream(emptyFile)) {
            workbook.createSheet("EmptySheet");
            workbook.write(outputStream);
        }
        
        // Configure parser
        ParseSetting settings = new ParseSetting();
        settings.setSheetIndex(0);
        excelParser.configure(settings);
        
        // Parse the workbook - should return empty list, not throw exception
        try (FileInputStream fis = new FileInputStream(emptyFile)) {
            List<String[]> results = excelParser.parse(fis)
                    .collect().asList()
                    .await().indefinitely();
            
            // Verify we got 0 rows
            assertEquals(0, results.size());
        }
    }
    
    // Helper method to create test Excel file with one sheet
    private File createTestExcelFile() throws Exception {
        File file = tempDir.resolve("test.xlsx").toFile();
        
        try (Workbook workbook = new XSSFWorkbook();
             FileOutputStream outputStream = new FileOutputStream(file)) {
            
            Sheet sheet = workbook.createSheet("TestSheet");
            
            // Create header row
            Row headerRow = sheet.createRow(0);
            headerRow.createCell(0).setCellValue("Header1");
            headerRow.createCell(1).setCellValue("Header2");
            headerRow.createCell(2).setCellValue("Header3");
            
            // Create data row 1
            Row dataRow1 = sheet.createRow(1);
            dataRow1.createCell(0).setCellValue("A1");
            dataRow1.createCell(1).setCellValue("B1");
            dataRow1.createCell(2).setCellValue("C1");
            
            // Create empty row
            Row emptyRow = sheet.createRow(2);
            emptyRow.createCell(0).setCellValue("");
            emptyRow.createCell(1).setCellValue("");
            emptyRow.createCell(2).setCellValue("");
            
            // Create data row 2
            Row dataRow2 = sheet.createRow(3);
            dataRow2.createCell(0).setCellValue("A2");
            dataRow2.createCell(1).setCellValue("B2");
            dataRow2.createCell(2).setCellValue("C2");
            
            workbook.write(outputStream);
        }
        
        return file;
    }
    
    // Helper method to create test Excel file with multiple sheets
    private File createMultiSheetExcelFile() throws Exception {
        File file = tempDir.resolve("multi-sheet.xlsx").toFile();
        
        try (Workbook workbook = new XSSFWorkbook();
             FileOutputStream outputStream = new FileOutputStream(file)) {
            
            // First sheet - same as test Excel file
            Sheet sheet1 = workbook.createSheet("FirstSheet");
            
            // Create header row
            Row headerRow = sheet1.createRow(0);
            headerRow.createCell(0).setCellValue("Header1");
            headerRow.createCell(1).setCellValue("Header2");
            headerRow.createCell(2).setCellValue("Header3");
            
            // Create data row 1
            Row dataRow1 = sheet1.createRow(1);
            dataRow1.createCell(0).setCellValue("A1");
            dataRow1.createCell(1).setCellValue("B1");
            dataRow1.createCell(2).setCellValue("C1");
            
            // Create empty row
            Row emptyRow = sheet1.createRow(2);
            emptyRow.createCell(0).setCellValue("");
            emptyRow.createCell(1).setCellValue("");
            emptyRow.createCell(2).setCellValue("");
            
            // Create data row 2
            Row dataRow2 = sheet1.createRow(3);
            dataRow2.createCell(0).setCellValue("A2");
            dataRow2.createCell(1).setCellValue("B2");
            dataRow2.createCell(2).setCellValue("C2");
            
            // Second sheet - different data
            Sheet sheet2 = workbook.createSheet("SecondSheet");
            
            // Create header row
            Row headerRow2 = sheet2.createRow(0);
            headerRow2.createCell(0).setCellValue("Col1");
            headerRow2.createCell(1).setCellValue("Col2");
            
            // Create data row
            Row dataRow = sheet2.createRow(1);
            dataRow.createCell(0).setCellValue("X1");
            dataRow.createCell(1).setCellValue("Y1");
            
            workbook.write(outputStream);
        }
        
        return file;
    }

}