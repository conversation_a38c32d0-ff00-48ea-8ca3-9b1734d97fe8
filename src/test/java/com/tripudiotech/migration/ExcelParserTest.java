package com.tripudiotech.migration;

import com.tripudiotech.migration.service.processor.file.parser.ExcelParser;
import org.dhatim.fastexcel.reader.Cell;
import org.dhatim.fastexcel.reader.CellType;
import org.dhatim.fastexcel.reader.Row;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ExcelParserTest {

    @InjectMocks
    private ExcelParser excelParser;

    @Mock
    private Row row;

    @Mock
    private Cell cell;

    @Test
    void testNumericCellAsDate() {
        when(row.getOptionalCell(0)).thenReturn(Optional.of(cell));
        when(cell.getType()).thenReturn(CellType.NUMBER);
        when(cell.asNumber()).thenReturn(BigDecimal.valueOf(43831)); // 2020-01-01

        String result = excelParser.getCellValueAsString(row, 0);

        assertEquals("2020-01-01", result);
    }

    @Test
    void testNumericCellAsDateTime() {
        when(row.getOptionalCell(0)).thenReturn(Optional.of(cell));
        when(cell.getType()).thenReturn(CellType.NUMBER);
        when(cell.asNumber()).thenReturn(BigDecimal.valueOf(43831.500001)); // just past 12:00 noon

        String result = excelParser.getCellValueAsString(row, 0);

        assertEquals("2020-01-01T12:00:00", result);
    }

    @Test
    void testNumericCellAsNumber() {
        when(row.getOptionalCell(0)).thenReturn(Optional.of(cell));
        when(cell.getType()).thenReturn(CellType.NUMBER);
        when(cell.asNumber()).thenReturn(BigDecimal.valueOf(1234.5678));

        String result = excelParser.getCellValueAsString(row, 0);

        assertEquals("1234.5678", result);
    }

    @Test
    void testStringCellAsDate() {
        when(row.getOptionalCell(0)).thenReturn(Optional.of(cell));
        when(cell.getType()).thenReturn(CellType.STRING);
        when(cell.asString()).thenReturn("2023-12-25");

        String result = excelParser.getCellValueAsString(row, 0);

        assertEquals("2023-12-25", result);
    }

    @Test
    void testBooleanCell() {
        when(row.getOptionalCell(0)).thenReturn(Optional.of(cell));
        when(cell.getType()).thenReturn(CellType.BOOLEAN);
        when(cell.asBoolean()).thenReturn(true);

        String result = excelParser.getCellValueAsString(row, 0);

        assertEquals("true", result);
    }

    @Test
    void testEmptyCell() {
        when(row.getOptionalCell(0)).thenReturn(Optional.of(cell));
        when(cell.getType()).thenReturn(CellType.EMPTY);

        String result = excelParser.getCellValueAsString(row, 0);

        assertEquals("", result);
    }

    @Test
    void testErrorCell() {
        when(row.getOptionalCell(0)).thenReturn(Optional.of(cell));
        when(cell.getType()).thenReturn(CellType.ERROR);

        String result = excelParser.getCellValueAsString(row, 0);

        assertEquals("#ERROR", result);
    }

    @Test
    void testMissingCell() {
        when(row.getOptionalCell(0)).thenReturn(Optional.empty());

        String result = excelParser.getCellValueAsString(row, 0);

        assertEquals("", result);
    }
}
